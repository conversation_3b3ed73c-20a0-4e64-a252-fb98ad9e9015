import cv2
import numpy as np
from paddleocr import PaddleOCR
import os
import json
from typing import List, <PERSON><PERSON>, Dict

class MeterDataExtractor:
    def __init__(self):
        """初始化电表数据提取器"""
        self.ocr = PaddleOCR(
            use_doc_orientation_classify=False,
            use_doc_unwarping=False,
            use_textline_orientation=False,
            lang='ch'  # 支持中文
        )
    
    def detect_display_area(self, image: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """
        检测电表数据显示区域
        返回: [(x, y, w, h), ...] 检测到的矩形区域列表
        """
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 高斯模糊
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # 自适应阈值处理
        thresh = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
        
        # 形态学操作 - 闭运算连接数字
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (20, 5))
        closed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(closed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 筛选可能的数据显示区域
        display_areas = []
        height, width = image.shape[:2]
        
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            
            # 过滤条件：
            # 1. 宽度应该足够显示多位数字
            # 2. 高度适中
            # 3. 面积不能太小
            # 4. 长宽比应该合理（数字显示通常是横向的）
            area = w * h
            aspect_ratio = w / h if h > 0 else 0
            
            if (w > 80 and h > 20 and h < height * 0.3 and 
                area > 2000 and aspect_ratio > 2 and aspect_ratio < 15):
                display_areas.append((x, y, w, h))
        
        # 按面积排序，返回最大的几个区域
        display_areas.sort(key=lambda x: x[2] * x[3], reverse=True)
        return display_areas[:3]  # 返回最大的3个区域
    
    def enhance_display_area(self, image: np.ndarray, x: int, y: int, w: int, h: int) -> np.ndarray:
        """
        增强数据显示区域的图像质量
        """
        # 提取ROI
        roi = image[y:y+h, x:x+w]
        
        # 转换为灰度图
        gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        
        # 直方图均衡化
        equalized = cv2.equalizeHist(gray)
        
        # 高斯模糊去噪
        denoised = cv2.GaussianBlur(equalized, (3, 3), 0)
        
        # 锐化
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(denoised, -1, kernel)
        
        # 二值化
        _, binary = cv2.threshold(sharpened, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 形态学操作清理噪声
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        return cleaned
    
    def extract_numbers_from_text(self, ocr_results: List) -> List[str]:
        """
        从OCR结果中提取数字
        """
        numbers = []
        for result in ocr_results:
            text = result[1][0]  # 获取识别的文本
            confidence = result[1][1]  # 获取置信度
            
            # 只保留高置信度的结果
            if confidence > 0.7:
                # 提取数字和小数点
                import re
                number_pattern = r'[\d\.]+'
                found_numbers = re.findall(number_pattern, text)
                numbers.extend(found_numbers)
        
        return numbers
    
    def process_image(self, image_path: str, output_dir: str = "meter_output") -> Dict:
        """
        处理单张电表图片
        """
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        # 检测数据显示区域
        display_areas = self.detect_display_area(image)
        
        results = {
            "image_path": image_path,
            "display_areas": [],
            "extracted_numbers": []
        }
        
        # 处理每个检测到的区域
        for i, (x, y, w, h) in enumerate(display_areas):
            # 在原图上标记检测区域
            cv2.rectangle(image, (x, y), (x+w, y+h), (0, 255, 0), 2)
            cv2.putText(image, f"Area {i+1}", (x, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # 增强显示区域
            enhanced_roi = self.enhance_display_area(image, x, y, w, h)
            
            # 保存增强后的ROI
            roi_filename = f"{os.path.splitext(os.path.basename(image_path))[0]}_area_{i+1}.jpg"
            roi_path = os.path.join(output_dir, roi_filename)
            cv2.imwrite(roi_path, enhanced_roi)
            
            # 对增强后的区域进行OCR
            ocr_result = self.ocr.ocr(enhanced_roi, cls=False)
            
            # 提取数字
            if ocr_result and ocr_result[0]:
                numbers = self.extract_numbers_from_text(ocr_result[0])
                
                area_result = {
                    "area_id": i + 1,
                    "bbox": [x, y, w, h],
                    "roi_path": roi_path,
                    "ocr_raw": ocr_result[0],
                    "extracted_numbers": numbers
                }
                results["display_areas"].append(area_result)
                results["extracted_numbers"].extend(numbers)
        
        # 保存标记后的原图
        marked_image_path = os.path.join(output_dir, 
                                       f"{os.path.splitext(os.path.basename(image_path))[0]}_marked.jpg")
        cv2.imwrite(marked_image_path, image)
        results["marked_image_path"] = marked_image_path
        
        # 保存结果到JSON
        json_path = os.path.join(output_dir, 
                                f"{os.path.splitext(os.path.basename(image_path))[0]}_results.json")
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        return results

def main():
    """主函数 - 处理所有电表图片"""
    extractor = MeterDataExtractor()
    
    # 获取所有jpg图片
    image_files = [f for f in os.listdir('.') if f.lower().endswith('.jpg')]
    
    print(f"找到 {len(image_files)} 张图片")
    
    all_results = []
    
    for image_file in image_files:
        print(f"\n处理图片: {image_file}")
        try:
            result = extractor.process_image(image_file)
            all_results.append(result)
            
            print(f"检测到 {len(result['display_areas'])} 个数据显示区域")
            print(f"提取到的数字: {result['extracted_numbers']}")
            
        except Exception as e:
            print(f"处理 {image_file} 时出错: {e}")
    
    # 保存汇总结果
    with open("meter_output/summary_results.json", 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n处理完成！结果保存在 meter_output 目录中")

if __name__ == "__main__":
    main()
