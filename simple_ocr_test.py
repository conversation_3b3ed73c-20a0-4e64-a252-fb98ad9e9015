from paddleocr import PaddleOCR
import re

def test_ocr(image_path):
    """简单测试OCR"""
    print(f"处理图片: {image_path}")
    
    # 初始化OCR
    ocr = PaddleOCR(
        use_doc_orientation_classify=False,
        use_doc_unwarping=False,
        use_textline_orientation=False,
        lang='ch'
    )
    
    # 进行OCR识别
    result = ocr.predict(input=image_path)
    
    print(f"OCR结果数量: {len(result)}")
    
    all_numbers = []
    
    for i, res in enumerate(result):
        print(f"\n=== 结果 {i+1} ===")
        
        # 直接打印结果
        try:
            res.print()
        except:
            pass
        
        # 获取文本和分数
        try:
            # 直接访问OCRResult对象的属性
            rec_texts = res['rec_texts']
            rec_scores = res['rec_scores']
            print(f"\n识别到 {len(rec_texts)} 个文本:")

            for j, (text, score) in enumerate(zip(rec_texts, rec_scores)):
                print(f"{j+1:2d}. 文本: '{text}' | 置信度: {score:.3f}")

                # 提取数字
                if score > 0.3:
                    numbers = re.findall(r'\d+\.?\d*', text)
                    for num in numbers:
                        if len(num) >= 3:  # 至少3位数字
                            all_numbers.append({
                                'value': num,
                                'confidence': score,
                                'original_text': text,
                                'position': j
                            })
                            print(f"    -> 提取数字: {num}")
        except Exception as e:
            print(f"访问OCR结果时出错: {e}")
            print(f"结果对象类型: {type(res)}")
            if hasattr(res, 'keys'):
                print(f"可用的键: {list(res.keys())}")
    
    print(f"\n=== 提取的数字 ===")
    for num in all_numbers:
        print(f"数值: {num['value']} | 置信度: {num['confidence']:.3f} | 原文: '{num['original_text']}'")
    
    return all_numbers

if __name__ == "__main__":
    # 测试第一张图片
    numbers = test_ocr("1.jpg")
    print(f"\n总共提取到 {len(numbers)} 个可能的电表读数")
