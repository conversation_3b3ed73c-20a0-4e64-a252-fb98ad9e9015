from paddleocr import PaddleOCR
import os
import json
import re
import cv2
import numpy as np

def detect_meter_display_area(image_path):
    """检测电表数字显示区域"""
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        return None, []

    # 转换为灰度图
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # 高斯模糊
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    # 自适应阈值处理
    thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)

    # 形态学操作 - 闭运算连接数字
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (20, 5))
    closed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

    # 查找轮廓
    contours, _ = cv2.findContours(closed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # 筛选可能的数据显示区域
    display_areas = []
    height, width = image.shape[:2]

    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        area = w * h
        aspect_ratio = w / h if h > 0 else 0

        # 过滤条件：寻找可能的数字显示区域
        if (w > 80 and h > 20 and h < height * 0.3 and
            area > 2000 and aspect_ratio > 2 and aspect_ratio < 15):
            display_areas.append((x, y, w, h))

    # 按面积排序，返回最大的几个区域
    display_areas.sort(key=lambda x: x[2] * x[3], reverse=True)
    return image, display_areas[:3]  # 返回最大的3个区域

def extract_meter_data(image_path):
    """提取电表数据显示区域并进行OCR识别"""
    print(f"处理图片: {image_path}")

    # 检测数字显示区域
    image, display_areas = detect_meter_display_area(image_path)
    if image is None:
        print("无法读取图像")
        return []

    print(f"检测到 {len(display_areas)} 个可能的数字显示区域")

    # 初始化OCR
    ocr = PaddleOCR(
        use_doc_orientation_classify=False,
        use_doc_unwarping=False,
        use_textline_orientation=False,
        lang='ch'
    )

    all_numbers = []

    # 如果检测到显示区域，优先处理这些区域
    if display_areas:
        for i, (x, y, w, h) in enumerate(display_areas):
            print(f"\n处理显示区域 {i+1}: ({x}, {y}, {w}, {h})")

            # 提取ROI
            roi = image[y:y+h, x:x+w]

            # 增强ROI图像质量
            gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
            enhanced_roi = cv2.equalizeHist(gray_roi)

            # 对ROI进行OCR
            result = ocr.predict(input=enhanced_roi)
            numbers = process_ocr_result(result, f"区域{i+1}")
            all_numbers.extend(numbers)

    # 如果没有检测到显示区域或结果不理想，对整张图片进行OCR
    if not display_areas or len(all_numbers) == 0:
        print("\n对整张图片进行OCR识别")
        result = ocr.predict(input=image_path)
        numbers = process_ocr_result(result, "整图")
        all_numbers.extend(numbers)

    return all_numbers

def process_ocr_result(result, source):
    """处理OCR结果，提取数字"""
    numbers = []

    if not result:
        print(f"{source}: 未检测到文本")
        return numbers

    print(f"\n{source} OCR识别结果:")

    for res in result:
        try:
            rec_texts = res['rec_texts']
            rec_scores = res['rec_scores']

            for i, (text, confidence) in enumerate(zip(rec_texts, rec_scores)):
                print(f"  文本: '{text}' | 置信度: {confidence:.3f}")

                # 提取数字（包括小数点）
                if confidence > 0.5:  # 提高置信度阈值
                    number_matches = re.findall(r'\d+\.?\d*', text)
                    for match in number_matches:
                        if len(match) >= 4:  # 电表读数通常至少4位数字
                            numbers.append({
                                'value': match,
                                'confidence': confidence,
                                'original_text': text,
                                'source': source
                            })
                            print(f"    -> 提取数字: {match}")
        except Exception as e:
            print(f"处理OCR结果时出错: {e}")

    return numbers

def main():
    """主函数"""
    # 创建输出目录
    os.makedirs("meter_results", exist_ok=True)
    
    # 获取所有jpg图片
    image_files = [f for f in os.listdir('.') if f.lower().endswith('.jpg')]
    
    print(f"找到 {len(image_files)} 张图片")
    
    all_results = {}
    
    for image_file in image_files:
        try:
            numbers = extract_meter_data(image_file)
            all_results[image_file] = numbers
            
            print(f"\n提取到的可能电表读数:")
            for num in numbers:
                print(f"  数值: {num['value']}, 置信度: {num['confidence']:.3f}")
            print("-" * 50)
            
        except Exception as e:
            print(f"处理 {image_file} 时出错: {e}")
            all_results[image_file] = []
    
    # 保存结果
    with open("meter_results/extracted_numbers.json", 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n结果已保存到 meter_results/extracted_numbers.json")
    
    # 显示汇总
    print("\n=== 汇总结果 ===")
    for image, numbers in all_results.items():
        print(f"{image}: {[n['value'] for n in numbers]}")

if __name__ == "__main__":
    main()
