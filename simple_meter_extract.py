from paddleocr import PaddleOCR
import os
import json
import re

def extract_meter_data(image_path):
    """简化版电表数据提取"""
    print(f"处理图片: {image_path}")
    
    # 初始化OCR
    ocr = PaddleOCR(
        use_doc_orientation_classify=False,
        use_doc_unwarping=False,
        use_textline_orientation=False,
        lang='ch'
    )
    
    # 进行OCR识别
    result = ocr.predict(input=image_path)
    
    if not result:
        print("未检测到文本")
        return []

    # 提取数字
    numbers = []
    print("\nOCR识别结果:")

    # 新版PaddleOCR返回的是结果对象列表
    for res in result:
        if hasattr(res, 'rec_texts') and hasattr(res, 'rec_scores'):
            # 遍历识别结果
            for i, (text, confidence) in enumerate(zip(res.rec_texts, res.rec_scores)):
                bbox = res.dt_polys[i] if hasattr(res, 'dt_polys') and i < len(res.dt_polys) else None

                print(f"文本: {text}, 置信度: {confidence:.3f}")

                # 提取数字（包括小数点）
                if confidence > 0.3:  # 降低置信度阈值
                    number_matches = re.findall(r'\d+\.?\d*', text)
                    for match in number_matches:
                        if len(match) >= 3:  # 至少3位数字才可能是电表读数
                            numbers.append({
                                'value': match,
                                'confidence': confidence,
                                'bbox': bbox.tolist() if bbox is not None else None,
                                'original_text': text
                            })
    
    return numbers

def main():
    """主函数"""
    # 创建输出目录
    os.makedirs("meter_results", exist_ok=True)
    
    # 获取所有jpg图片
    image_files = [f for f in os.listdir('.') if f.lower().endswith('.jpg')]
    
    print(f"找到 {len(image_files)} 张图片")
    
    all_results = {}
    
    for image_file in image_files:
        try:
            numbers = extract_meter_data(image_file)
            all_results[image_file] = numbers
            
            print(f"\n提取到的可能电表读数:")
            for num in numbers:
                print(f"  数值: {num['value']}, 置信度: {num['confidence']:.3f}")
            print("-" * 50)
            
        except Exception as e:
            print(f"处理 {image_file} 时出错: {e}")
            all_results[image_file] = []
    
    # 保存结果
    with open("meter_results/extracted_numbers.json", 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n结果已保存到 meter_results/extracted_numbers.json")
    
    # 显示汇总
    print("\n=== 汇总结果 ===")
    for image, numbers in all_results.items():
        print(f"{image}: {[n['value'] for n in numbers]}")

if __name__ == "__main__":
    main()
