from paddleocr import PaddleOCR
import json

def debug_ocr(image_path):
    """调试OCR结果"""
    print(f"调试图片: {image_path}")
    
    # 初始化OCR
    ocr = PaddleOCR(
        use_doc_orientation_classify=False,
        use_doc_unwarping=False,
        use_textline_orientation=False,
        lang='ch'
    )
    
    # 进行OCR识别
    result = ocr.predict(input=image_path)
    
    print(f"OCR结果类型: {type(result)}")
    print(f"OCR结果长度: {len(result) if result else 0}")
    
    if result:
        for i, res in enumerate(result):
            print(f"\n结果 {i+1}:")
            print(f"  类型: {type(res)}")
            print(f"  属性: {dir(res)}")
            
            # 检查常见属性
            if hasattr(res, 'rec_text'):
                print(f"  rec_text: {res.rec_text}")
            if hasattr(res, 'rec_score'):
                print(f"  rec_score: {res.rec_score}")
            if hasattr(res, 'dt_polys'):
                print(f"  dt_polys 长度: {len(res.dt_polys) if res.dt_polys is not None else 0}")
            
            # 尝试打印结果
            try:
                res.print()
            except Exception as e:
                print(f"  打印结果时出错: {e}")
    else:
        print("OCR结果为空")

if __name__ == "__main__":
    # 测试第一张图片
    debug_ocr("1.jpg")
